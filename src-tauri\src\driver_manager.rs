use crate::error::{HoutError, Result};
use crate::device::CommandResult;
use std::path::PathBuf;
use std::process::Command;
use log::{info, warn, error};

/// 驱动管理器
pub struct DriverManager {
    driver_path: PathBuf,
}

impl DriverManager {
    /// 创建新的驱动管理器实例
    pub fn new() -> Self {
        let driver_path = Self::get_driver_directory();
        Self { driver_path }
    }

    /// 获取驱动程序目录
    fn get_driver_directory() -> PathBuf {
        // 优先从应用程序资源目录获取
        if let Ok(exe_path) = std::env::current_exe() {
            if let Some(exe_dir) = exe_path.parent() {
                let resource_path = exe_dir.join("resources").join("drivers");
                if resource_path.exists() {
                    info!("Found driver directory at: {}", resource_path.display());
                    return resource_path;
                }
            }
        }

        // 备用路径：开发环境
        let dev_path = PathBuf::from("src-tauri/resources/drivers");
        if dev_path.exists() {
            info!("Using development driver path: {}", dev_path.display());
            return dev_path;
        }

        // 默认路径
        warn!("Driver directory not found, using default path");
        PathBuf::from("drivers")
    }

    /// 检查驱动文件是否存在
    pub fn check_driver_files(&self) -> Result<bool> {
        let inf_file = self.driver_path.join("android_winusb.inf");
        let coinstaller = self.driver_path.join("WinUSBCoInstaller2.dll");
        
        let files_exist = inf_file.exists() && coinstaller.exists();
        
        if files_exist {
            info!("All required driver files found");
        } else {
            warn!("Missing driver files:");
            if !inf_file.exists() {
                warn!("  - android_winusb.inf not found");
            }
            if !coinstaller.exists() {
                warn!("  - WinUSBCoInstaller2.dll not found");
            }
        }
        
        Ok(files_exist)
    }

    /// 安装Android USB驱动
    #[cfg(windows)]
    pub async fn install_driver(&self) -> Result<CommandResult> {
        info!("Starting Android USB driver installation");

        // 检查驱动文件
        if !self.check_driver_files()? {
            return Ok(CommandResult {
                success: false,
                output: String::new(),
                error: Some("驱动文件不完整，请检查resources/drivers目录".to_string()),
                exit_code: Some(1),
            });
        }

        let inf_path = self.driver_path.join("android_winusb.inf");
        
        // 方法1: 使用pnputil安装驱动
        match self.install_with_pnputil(&inf_path).await {
            Ok(result) if result.success => return Ok(result),
            Ok(result) => {
                warn!("pnputil installation failed: {:?}", result.error);
            }
            Err(e) => {
                warn!("pnputil command failed: {}", e);
            }
        }

        // 方法2: 使用dpinst作为备用方案
        self.install_with_dpinst().await
    }

    /// 使用pnputil安装驱动
    #[cfg(windows)]
    async fn install_with_pnputil(&self, inf_path: &PathBuf) -> Result<CommandResult> {
        info!("Installing driver using pnputil");

        let mut cmd = Command::new("pnputil");
        cmd.args(&["/add-driver", &inf_path.to_string_lossy(), "/install"]);

        // 隐藏命令行窗口
        use std::os::windows::process::CommandExt;
        const CREATE_NO_WINDOW: u32 = 0x08000000;
        cmd.creation_flags(CREATE_NO_WINDOW);

        match cmd.output() {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout).to_string();
                let stderr = String::from_utf8_lossy(&output.stderr).to_string();

                if output.status.success() {
                    info!("Driver installed successfully with pnputil");
                    Ok(CommandResult {
                        success: true,
                        output: "Android USB驱动安装成功".to_string(),
                        error: None,
                        exit_code: Some(0),
                    })
                } else {
                    warn!("pnputil installation failed: {}", stderr);
                    Ok(CommandResult {
                        success: false,
                        output: stdout,
                        error: Some(format!("pnputil安装失败: {}", stderr)),
                        exit_code: output.status.code(),
                    })
                }
            }
            Err(e) => {
                error!("Failed to execute pnputil: {}", e);
                Err(HoutError::IoError {
                    message: format!("执行pnputil命令失败: {}", e),
                })
            }
        }
    }

    /// 使用dpinst安装驱动
    #[cfg(windows)]
    async fn install_with_dpinst(&self) -> Result<CommandResult> {
        info!("Installing driver using dpinst");

        let dpinst_path = self.driver_path.join("dpinst.exe");
        
        if !dpinst_path.exists() {
            return Ok(CommandResult {
                success: false,
                output: String::new(),
                error: Some("dpinst.exe不存在，无法使用备用安装方法".to_string()),
                exit_code: Some(1),
            });
        }

        let mut cmd = Command::new(&dpinst_path);
        cmd.args(&["/sw", "/path", &self.driver_path.to_string_lossy()]);

        // 隐藏命令行窗口
        use std::os::windows::process::CommandExt;
        const CREATE_NO_WINDOW: u32 = 0x08000000;
        cmd.creation_flags(CREATE_NO_WINDOW);

        match cmd.output() {
            Ok(output) => {
                if output.status.success() {
                    info!("Driver installed successfully with dpinst");
                    Ok(CommandResult {
                        success: true,
                        output: "Android USB驱动安装成功 (使用dpinst)".to_string(),
                        error: None,
                        exit_code: Some(0),
                    })
                } else {
                    let stderr = String::from_utf8_lossy(&output.stderr).to_string();
                    warn!("dpinst installation failed: {}", stderr);
                    Ok(CommandResult {
                        success: false,
                        output: String::from_utf8_lossy(&output.stdout).to_string(),
                        error: Some(format!("dpinst安装失败: {}", stderr)),
                        exit_code: output.status.code(),
                    })
                }
            }
            Err(e) => {
                error!("Failed to execute dpinst: {}", e);
                Err(HoutError::IoError {
                    message: format!("执行dpinst命令失败: {}", e),
                })
            }
        }
    }

    /// 非Windows系统的驱动安装
    #[cfg(not(windows))]
    pub async fn install_driver(&self) -> Result<CommandResult> {
        Ok(CommandResult {
            success: false,
            output: String::new(),
            error: Some("Android USB驱动安装功能仅在Windows系统上可用".to_string()),
            exit_code: Some(1),
        })
    }

    /// 检查驱动安装状态
    pub async fn check_driver_status(&self) -> Result<bool> {
        // 通过检查设备管理器中是否有Android设备来判断驱动状态
        // 这里可以扩展更详细的检查逻辑
        info!("Checking driver installation status");
        
        #[cfg(windows)]
        {
            // 使用PowerShell检查设备状态
            let mut cmd = Command::new("powershell");
            cmd.args(&["-Command", "Get-PnpDevice | Where-Object {$_.FriendlyName -like '*Android*' -or $_.FriendlyName -like '*ADB*'} | Select-Object Status"]);
            
            use std::os::windows::process::CommandExt;
            const CREATE_NO_WINDOW: u32 = 0x08000000;
            cmd.creation_flags(CREATE_NO_WINDOW);
            
            match cmd.output() {
                Ok(output) if output.status.success() => {
                    let output_str = String::from_utf8_lossy(&output.stdout);
                    let has_ok_devices = output_str.contains("OK");
                    info!("Driver status check result: {}", has_ok_devices);
                    Ok(has_ok_devices)
                }
                _ => {
                    warn!("Failed to check driver status");
                    Ok(false)
                }
            }
        }
        
        #[cfg(not(windows))]
        {
            Ok(true) // 非Windows系统默认返回true
        }
    }
}

impl Default for DriverManager {
    fn default() -> Self {
        Self::new()
    }
}
