# 🎯 简化后的环境变量配置
# 统一数据库架构 - 单一数据库配置

# 🗄️ 统一数据库配置
DATABASE_URL='postgresql://username:password@hostname:port/unified_db?sslmode=require'

# 🔑 API 安全配置
API_KEY=your-secret-api-key-here
ENABLE_API_KEY_AUTH=true
API_KEY_EXPIRATION_HOURS=24

# 🛡️ GitHub OAuth 配置
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
REDIRECT_URI=http://localhost:3000/api/auth/github/callback

# 🔐 JWT 配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# 👤 管理员权限配置
ALLOWED_GITHUB_USERNAME=your-github-username
ALLOWED_GITHUB_EMAIL=<EMAIL>

# 🌐 CORS 和安全配置
FRONTEND_URL=http://localhost:3000/admin
ALLOWED_ORIGINS=https://admin.lacs.cc,http://localhost:3000

# 📊 用户行为统计配置
USER_BEHAVIOR_API_KEY=your-user-behavior-api-key
WANJIGUANJIA_APP_ID=your-app-id
WANJIGUANJIA_APP_SECRET=your-app-secret
JWT_EXPIRATION_HOURS=24

# 🚀 性能和安全配置
ENABLE_RATE_LIMITING=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000

# 🌍 环境配置
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3000/app

# 📈 监控配置 (可选)
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true

# 🔧 数据库优化配置 (可选)
DB_POOL_SIZE=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=5000

# 📝 日志配置 (可选)
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_SQL_LOGGING=false

# 🧹 自动清理配置 (可选)
AUTO_CLEANUP_ENABLED=true
CLEANUP_INTERVAL_HOURS=24
EXPIRED_CODES_RETENTION_DAYS=30
OLD_STATS_RETENTION_DAYS=90

# ⚠️ 迁移配置 (仅在迁移期间使用)
# 迁移完成后可以删除这些配置
# ACTIVATION_CODES_DATABASE_URL='postgresql://username:password@hostname:port/activation_codes_db?sslmode=require'
# SOFTWARE_DATABASE_URL='postgresql://username:password@hostname:port/software_db?sslmode=require'
# USER_BEHAVIOR_DATABASE_URL='postgresql://username:password@hostname:port/user_behavior_db?sslmode=require'
