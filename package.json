{"scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "migrate:device-connections": "node scripts/migrate-device-connections-simplify.js", "migrate:unified": "node scripts/migrate-to-unified-database.js", "migrate:unified:dry-run": "node scripts/migrate-to-unified-database.js --dry-run", "analyze:consolidation": "node scripts/database-consolidation-plan.js", "deploy:check": "node scripts/deploy-check.js", "deploy:build": "npm run deploy:check && npm run build"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/nextjs-registry": "^1.1.0", "@hono/node-server": "^1.17.1", "@neondatabase/serverless": "^1.0.1", "@types/jsonwebtoken": "^9.0.10", "antd": "^5.26.6", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "hono": "^4.8.9", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "next": "^14.2.3", "react": "18.3.1", "react-dom": "18.3.1", "uuid": "^11.1.0", "zod": "^4.0.10"}, "devDependencies": {"@types/node": "18.11.18", "@types/react": "18.0.26", "@types/react-dom": "18.0.10", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.19", "critters": "^0.0.23", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.8.3"}}