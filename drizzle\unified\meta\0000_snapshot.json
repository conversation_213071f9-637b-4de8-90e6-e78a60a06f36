{"id": "483d3549-87b1-49e4-93f0-4a17554556cf", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.activation_codes": {"name": "activation_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "used_at": {"name": "used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "used_by": {"name": "used_by", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "product_info": {"name": "product_info", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"activation_codes_code_unique": {"name": "activation_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.download_stats": {"name": "download_stats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "version_id": {"name": "version_id", "type": "integer", "primaryKey": false, "notNull": false}, "download_source": {"name": "download_source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "download_count": {"name": "download_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_download_at": {"name": "last_download_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"download_stats_software_id_software_id_fk": {"name": "download_stats_software_id_software_id_fk", "tableFrom": "download_stats", "tableTo": "software", "columnsFrom": ["software_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "download_stats_version_id_software_version_history_id_fk": {"name": "download_stats_version_id_software_version_history_id_fk", "tableFrom": "download_stats", "tableTo": "software_version_history", "columnsFrom": ["version_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software": {"name": "software", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name_en": {"name": "name_en", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "description_en": {"name": "description_en", "type": "text", "primaryKey": false, "notNull": false}, "current_version": {"name": "current_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "official_website": {"name": "official_website", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "system_requirements": {"name": "system_requirements", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"software_name_unique": {"name": "software_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software_announcements": {"name": "software_announcements", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "title_en": {"name": "title_en", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "content_en": {"name": "content_en", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'general'"}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'normal'"}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"software_announcements_software_id_software_id_fk": {"name": "software_announcements_software_id_software_id_fk", "tableFrom": "software_announcements", "tableTo": "software", "columnsFrom": ["software_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software_version_history": {"name": "software_version_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "release_date": {"name": "release_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "release_notes": {"name": "release_notes", "type": "text", "primaryKey": false, "notNull": false}, "release_notes_en": {"name": "release_notes_en", "type": "text", "primaryKey": false, "notNull": false}, "download_links": {"name": "download_links", "type": "jsonb", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "file_size_bytes": {"name": "file_size_bytes", "type": "integer", "primaryKey": false, "notNull": false}, "file_hash": {"name": "file_hash", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "is_stable": {"name": "is_stable", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_beta": {"name": "is_beta", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_prerelease": {"name": "is_prerelease", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "version_type": {"name": "version_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'release'"}, "changelog_category": {"name": "changelog_category", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"software_version_history_software_id_software_id_fk": {"name": "software_version_history_software_id_software_id_fk", "tableFrom": "software_version_history", "tableTo": "software", "columnsFrom": ["software_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.behavior_stats": {"name": "behavior_stats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "stat_type": {"name": "stat_type", "type": "text", "primaryKey": false, "notNull": true}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "stat_data": {"name": "stat_data", "type": "text", "primaryKey": false, "notNull": true}, "stat_date": {"name": "stat_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "stat_period": {"name": "stat_period", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"behavior_stats_stat_type_idx": {"name": "behavior_stats_stat_type_idx", "columns": [{"expression": "stat_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "behavior_stats_stat_date_idx": {"name": "behavior_stats_stat_date_idx", "columns": [{"expression": "stat_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "behavior_stats_software_id_idx": {"name": "behavior_stats_software_id_idx", "columns": [{"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "behavior_stats_unique": {"name": "behavior_stats_unique", "columns": [{"expression": "stat_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "stat_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "stat_period", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.device_connections": {"name": "device_connections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "device_serial": {"name": "device_serial", "type": "text", "primaryKey": false, "notNull": true}, "device_brand": {"name": "device_brand", "type": "text", "primaryKey": false, "notNull": false}, "device_model": {"name": "device_model", "type": "text", "primaryKey": false, "notNull": false}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_device_fingerprint": {"name": "user_device_fingerprint", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"device_connections_device_serial_idx": {"name": "device_connections_device_serial_idx", "columns": [{"expression": "device_serial", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "device_connections_software_id_idx": {"name": "device_connections_software_id_idx", "columns": [{"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "device_connections_user_device_fingerprint_idx": {"name": "device_connections_user_device_fingerprint_idx", "columns": [{"expression": "user_device_fingerprint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software_activations": {"name": "software_activations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "software_name": {"name": "software_name", "type": "text", "primaryKey": false, "notNull": true, "default": "'玩机管家'"}, "software_version": {"name": "software_version", "type": "text", "primaryKey": false, "notNull": false}, "device_fingerprint": {"name": "device_fingerprint", "type": "text", "primaryKey": false, "notNull": true}, "device_os": {"name": "device_os", "type": "text", "primaryKey": false, "notNull": false}, "device_arch": {"name": "device_arch", "type": "text", "primaryKey": false, "notNull": false}, "activation_code": {"name": "activation_code", "type": "text", "primaryKey": false, "notNull": false}, "activated_at": {"name": "activated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "user_email": {"name": "user_email", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "region": {"name": "region", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"software_activations_device_fingerprint_idx": {"name": "software_activations_device_fingerprint_idx", "columns": [{"expression": "device_fingerprint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "software_activations_software_id_idx": {"name": "software_activations_software_id_idx", "columns": [{"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "software_activations_activated_at_idx": {"name": "software_activations_activated_at_idx", "columns": [{"expression": "activated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "software_activations_device_fingerprint_unique": {"name": "software_activations_device_fingerprint_unique", "columns": [{"expression": "device_fingerprint", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}