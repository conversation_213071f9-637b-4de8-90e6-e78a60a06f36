{"id": "ddbdba10-9d29-40fc-b543-df74a59a978f", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.behavior_stats": {"name": "behavior_stats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "stat_type": {"name": "stat_type", "type": "text", "primaryKey": false, "notNull": true}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "stat_data": {"name": "stat_data", "type": "text", "primaryKey": false, "notNull": true}, "stat_date": {"name": "stat_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "stat_period": {"name": "stat_period", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"behavior_stats_stat_type_idx": {"name": "behavior_stats_stat_type_idx", "columns": [{"expression": "stat_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "behavior_stats_stat_date_idx": {"name": "behavior_stats_stat_date_idx", "columns": [{"expression": "stat_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "behavior_stats_software_id_idx": {"name": "behavior_stats_software_id_idx", "columns": [{"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "behavior_stats_unique": {"name": "behavior_stats_unique", "columns": [{"expression": "stat_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "stat_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "stat_period", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.device_connections": {"name": "device_connections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "device_serial": {"name": "device_serial", "type": "text", "primaryKey": false, "notNull": true}, "device_brand": {"name": "device_brand", "type": "text", "primaryKey": false, "notNull": false}, "device_model": {"name": "device_model", "type": "text", "primaryKey": false, "notNull": false}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_device_fingerprint": {"name": "user_device_fingerprint", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"device_connections_device_serial_idx": {"name": "device_connections_device_serial_idx", "columns": [{"expression": "device_serial", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "device_connections_software_id_idx": {"name": "device_connections_software_id_idx", "columns": [{"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "device_connections_user_device_fingerprint_idx": {"name": "device_connections_user_device_fingerprint_idx", "columns": [{"expression": "user_device_fingerprint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software_activations": {"name": "software_activations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "software_id": {"name": "software_id", "type": "integer", "primaryKey": false, "notNull": true}, "software_name": {"name": "software_name", "type": "text", "primaryKey": false, "notNull": true, "default": "'玩机管家'"}, "software_version": {"name": "software_version", "type": "text", "primaryKey": false, "notNull": false}, "device_fingerprint": {"name": "device_fingerprint", "type": "text", "primaryKey": false, "notNull": true}, "device_os": {"name": "device_os", "type": "text", "primaryKey": false, "notNull": false}, "device_arch": {"name": "device_arch", "type": "text", "primaryKey": false, "notNull": false}, "activation_code": {"name": "activation_code", "type": "text", "primaryKey": false, "notNull": false}, "activated_at": {"name": "activated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "user_email": {"name": "user_email", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "region": {"name": "region", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"software_activations_device_fingerprint_idx": {"name": "software_activations_device_fingerprint_idx", "columns": [{"expression": "device_fingerprint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "software_activations_software_id_idx": {"name": "software_activations_software_id_idx", "columns": [{"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "software_activations_activated_at_idx": {"name": "software_activations_activated_at_idx", "columns": [{"expression": "activated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "software_activations_device_fingerprint_unique": {"name": "software_activations_device_fingerprint_unique", "columns": [{"expression": "device_fingerprint", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "software_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}