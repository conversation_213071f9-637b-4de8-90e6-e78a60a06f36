import React, { useState, useEffect } from 'react';
import {
  Card,
  CardHeader,
  CardPreview,
  Text,
  Button,
  Badge,
  Spinner,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions,
  MessageBar,
  MessageBarBody,
  MessageBarTitle,
} from '@fluentui/react-components';
import {
  Settings24Regular,
  CheckmarkCircle24Filled,
  ErrorCircle24Filled,
  Warning24Filled,
  ArrowDownload24Regular,
} from '@fluentui/react-icons';
import { invoke } from '@tauri-apps/api/tauri';
import { CommandResult } from '../../types/device';

interface DriverStatus {
  isInstalled: boolean;
  filesComplete: boolean;
  lastChecked: Date;
}

export const DriverManagerCard: React.FC = () => {
  const [driverStatus, setDriverStatus] = useState<DriverStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [showInstallDialog, setShowInstallDialog] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'warning'; text: string } | null>(null);

  // 检查驱动状态
  const checkDriverStatus = async () => {
    setIsLoading(true);
    try {
      const [isInstalled, filesComplete] = await Promise.all([
        invoke<boolean>('check_driver_status'),
        invoke<boolean>('check_driver_files'),
      ]);

      setDriverStatus({
        isInstalled,
        filesComplete,
        lastChecked: new Date(),
      });

      if (!filesComplete) {
        setMessage({
          type: 'warning',
          text: '驱动文件不完整，请重新下载或安装应用程序',
        });
      } else if (!isInstalled) {
        setMessage({
          type: 'warning',
          text: '检测到Android设备连接问题，建议安装USB驱动程序',
        });
      } else {
        setMessage({
          type: 'success',
          text: 'Android USB驱动程序运行正常',
        });
      }
    } catch (error) {
      console.error('检查驱动状态失败:', error);
      setMessage({
        type: 'error',
        text: `检查驱动状态失败: ${error}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 安装驱动程序
  const installDriver = async () => {
    setIsInstalling(true);
    setShowInstallDialog(false);
    
    try {
      const result = await invoke<CommandResult>('install_device_driver');
      
      if (result.success) {
        setMessage({
          type: 'success',
          text: result.output || '驱动程序安装成功',
        });
        // 重新检查状态
        setTimeout(() => checkDriverStatus(), 2000);
      } else {
        setMessage({
          type: 'error',
          text: result.error || '驱动程序安装失败',
        });
      }
    } catch (error) {
      console.error('安装驱动失败:', error);
      setMessage({
        type: 'error',
        text: `安装驱动失败: ${error}`,
      });
    } finally {
      setIsInstalling(false);
    }
  };

  // 组件挂载时检查状态
  useEffect(() => {
    checkDriverStatus();
  }, []);

  // 获取状态图标和颜色
  const getStatusIcon = () => {
    if (!driverStatus) return <Settings24Regular />;
    
    if (!driverStatus.filesComplete) {
      return <ErrorCircle24Filled style={{ color: '#d13438' }} />;
    } else if (!driverStatus.isInstalled) {
      return <Warning24Filled style={{ color: '#f7630c' }} />;
    } else {
      return <CheckmarkCircle24Filled style={{ color: '#107c10' }} />;
    }
  };

  const getStatusText = () => {
    if (!driverStatus) return '检查中...';
    
    if (!driverStatus.filesComplete) {
      return '文件缺失';
    } else if (!driverStatus.isInstalled) {
      return '未安装';
    } else {
      return '已安装';
    }
  };

  const getStatusColor = (): 'success' | 'warning' | 'danger' | 'subtle' => {
    if (!driverStatus) return 'subtle';
    
    if (!driverStatus.filesComplete) {
      return 'danger';
    } else if (!driverStatus.isInstalled) {
      return 'warning';
    } else {
      return 'success';
    }
  };

  return (
    <>
      <Card style={{ height: '100%' }}>
        <CardHeader
          image={getStatusIcon()}
          header={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Text weight="semibold">USB驱动管理</Text>
              <Badge color={getStatusColor()} size="small">
                {getStatusText()}
              </Badge>
            </div>
          }
          description="管理Android设备USB驱动程序"
        />

        <CardPreview style={{ padding: '16px' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {/* 状态信息 */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Text size={200}>
                <strong>驱动状态:</strong> {getStatusText()}
              </Text>
              {driverStatus && (
                <>
                  <Text size={200}>
                    <strong>文件完整性:</strong> {driverStatus.filesComplete ? '完整' : '不完整'}
                  </Text>
                  <Text size={200}>
                    <strong>最后检查:</strong> {driverStatus.lastChecked.toLocaleTimeString()}
                  </Text>
                </>
              )}
            </div>

            {/* 操作按钮 */}
            <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              <Button
                size="small"
                onClick={checkDriverStatus}
                disabled={isLoading || isInstalling}
                icon={isLoading ? <Spinner size="tiny" /> : undefined}
              >
                {isLoading ? '检查中...' : '刷新状态'}
              </Button>

              <Dialog open={showInstallDialog} onOpenChange={(_, data) => setShowInstallDialog(data.open)}>
                <DialogTrigger disableButtonEnhancement>
                  <Button
                    size="small"
                    appearance="primary"
                    disabled={isLoading || isInstalling || (driverStatus?.filesComplete === false)}
                    icon={isInstalling ? <Spinner size="tiny" /> : <ArrowDownload24Regular />}
                  >
                    {isInstalling ? '安装中...' : '安装驱动'}
                  </Button>
                </DialogTrigger>
                <DialogSurface>
                  <DialogTitle>安装Android USB驱动程序</DialogTitle>
                  <DialogContent>
                    <DialogBody>
                      <Text>
                        即将安装Android USB驱动程序，这将帮助您的计算机正确识别Android设备。
                      </Text>
                      <br />
                      <Text>
                        <strong>注意事项：</strong>
                      </Text>
                      <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                        <li>安装过程可能需要管理员权限</li>
                        <li>请确保已连接需要安装驱动的Android设备</li>
                        <li>安装完成后建议重新连接设备</li>
                      </ul>
                    </DialogBody>
                    <DialogActions>
                      <Button
                        appearance="secondary"
                        onClick={() => setShowInstallDialog(false)}
                      >
                        取消
                      </Button>
                      <Button
                        appearance="primary"
                        onClick={installDriver}
                      >
                        确认安装
                      </Button>
                    </DialogActions>
                  </DialogContent>
                </DialogSurface>
              </Dialog>
            </div>
          </div>
        </CardPreview>
      </Card>

      {/* 消息提示 */}
      {message && (
        <MessageBar
          intent={message.type === 'success' ? 'success' : message.type === 'warning' ? 'warning' : 'error'}
          style={{ marginTop: '16px' }}
        >
          <MessageBarBody>
            <MessageBarTitle>
              {message.type === 'success' ? '成功' : message.type === 'warning' ? '警告' : '错误'}
            </MessageBarTitle>
            {message.text}
          </MessageBarBody>
        </MessageBar>
      )}
    </>
  );
};
